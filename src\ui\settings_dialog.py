#!/usr/bin/env python3
"""
设置对话框模块 - 提供常规设置和高级设置界面

分为两个标签页：
- 常规设置：压缩模型选择
- 高级设置：所有其他配置项
"""

import sys
from pathlib import Path
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
    QWidget, QLabel, QPushButton, QComboBox, QCheckBox,
    QSpinBox, QDoubleSpinBox, QLineEdit, QTextEdit,
    QGroupBox, QFormLayout, QScrollArea, QMessageBox,
    QSizePolicy, QFrame, QProgressDialog, QProgressBar,
    QApplication
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.config import get_config, update_config
from .theme_manager import get_theme_manager


class SettingsDialog(QDialog):
    """设置对话框"""
    
    settingsChanged = pyqtSignal()  # 设置改变信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config = get_config()
        self.theme_manager = get_theme_manager()
        self.init_ui()
        self.apply_theme()
        self.load_settings()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("设置")
        self.setModal(True)
        self.resize(600, 500)

        # 设置最小尺寸，确保窗口可以缩放但不会太小
        self.setMinimumSize(450, 350)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        # 样式将在apply_theme中设置
        
        # 常规设置标签页
        self.general_tab = self.create_general_tab()
        self.tab_widget.addTab(self.general_tab, "常规设置")
        
        # 高级设置标签页
        self.advanced_tab = self.create_advanced_tab()
        self.tab_widget.addTab(self.advanced_tab, "高级设置")
        
        layout.addWidget(self.tab_widget)
        
        # 按钮区域
        self.create_button_area(layout)
        
    def create_general_tab(self):
        """创建常规设置标签页"""
        # 创建滚动区域，与高级设置标签页保持一致
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 压缩设置组
        self.compression_group = QGroupBox("文本压缩设置")
        # 样式将在apply_theme中设置
        compression_layout = QFormLayout(self.compression_group)
        compression_layout.setSpacing(15)
        
        # 启用压缩
        self.enable_compression_cb = QCheckBox("启用文本压缩")
        # 样式将在apply_theme中设置
        compression_layout.addRow(self.enable_compression_cb)

        # LLM类型选择
        self.llm_type_combo = QComboBox()
        llm_types = [
            ("local", "本地模型 - 使用本地Transformers模型"),
            ("api", "API调用 - 使用在线大模型API")
        ]
        for value, description in llm_types:
            self.llm_type_combo.addItem(description, value)
        # 连接选择改变信号
        self.llm_type_combo.currentTextChanged.connect(self.on_llm_type_changed)
        compression_layout.addRow("LLM类型:", self.llm_type_combo)
        
        # 压缩模型选择
        self.compression_model_combo = QComboBox()
        # 样式将在apply_theme中设置
        # 添加常用压缩模型
        compression_models = [
            "Qwen/Qwen2.5-1.5B-Instruct",
            "Qwen/Qwen2.5-3B-Instruct",
            "Qwen/Qwen2.5-7B-Instruct",
            "自定义模型..."
        ]
        self.compression_model_combo.addItems(compression_models)
        # 连接选择改变信号
        self.compression_model_combo.currentTextChanged.connect(self.on_model_selection_changed)
        compression_layout.addRow("本地模型:", self.compression_model_combo)

        # API配置组
        self.api_config_group = QGroupBox("API配置")
        api_layout = QFormLayout(self.api_config_group)
        api_layout.setSpacing(10)

        # API提供商选择
        self.api_provider_combo = QComboBox()
        providers = [
            ("openai", "OpenAI")
        ]
        for value, description in providers:
            self.api_provider_combo.addItem(description, value)
        self.api_provider_combo.currentTextChanged.connect(self.on_api_provider_changed)
        api_layout.addRow("API提供商:", self.api_provider_combo)

        # API模型选择
        self.api_model_combo = QComboBox()
        self.api_model_combo.setEditable(True)  # 支持自定义输入
        self.api_model_combo.setInsertPolicy(QComboBox.InsertPolicy.NoInsert)  # 不自动插入
        api_layout.addRow("API模型:", self.api_model_combo)

        # API密钥输入（带查看/隐藏和检测按钮）
        api_key_layout = QHBoxLayout()

        self.api_key_edit = QLineEdit()
        self.api_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.api_key_edit.setPlaceholderText("请输入API密钥")
        api_key_layout.addWidget(self.api_key_edit)

        # 查看/隐藏密钥按钮
        self.toggle_key_visibility_btn = QPushButton("👁")
        self.toggle_key_visibility_btn.setFixedSize(30, 30)
        self.toggle_key_visibility_btn.setToolTip("显示/隐藏API密钥")
        self.toggle_key_visibility_btn.clicked.connect(self.toggle_api_key_visibility)
        api_key_layout.addWidget(self.toggle_key_visibility_btn)

        # API连通性检测按钮
        self.test_api_btn = QPushButton("检测")
        self.test_api_btn.setFixedSize(60, 30)
        self.test_api_btn.setToolTip("检测API连通性")
        self.test_api_btn.clicked.connect(self.test_api_connection)
        api_key_layout.addWidget(self.test_api_btn)

        api_key_widget = QWidget()
        api_key_widget.setLayout(api_key_layout)
        api_layout.addRow("API密钥:", api_key_widget)

        # API端点URL
        self.api_base_url_edit = QLineEdit()
        self.api_base_url_edit.setPlaceholderText("API端点URL (留空使用默认)")
        api_layout.addRow("API端点:", self.api_base_url_edit)

        compression_layout.addRow(self.api_config_group)

        # 初始时隐藏API配置组
        self.api_config_group.setVisible(False)
        
        layout.addWidget(self.compression_group)

        # 界面设置组（从高级设置移过来）
        self.create_ui_settings_group(layout)

        # Google Fonts API设置组
        self.create_google_fonts_group(layout)

        layout.addStretch()
        scroll_area.setWidget(widget)

        return scroll_area
        
    def create_advanced_tab(self):
        """创建高级设置标签页"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # OCR设置组
        self.create_ocr_settings_group(layout)
        
        # 翻译设置组
        self.create_translation_settings_group(layout)
        
        # 设备设置组
        self.create_device_settings_group(layout)

        # LLM提示词配置组
        self.create_llm_prompt_settings_group(layout)

        layout.addStretch()
        scroll_area.setWidget(widget)
        
        return scroll_area
        
    def create_ui_settings_group(self, layout):
        """创建界面设置组"""
        group = QGroupBox("界面设置")
        group.setStyleSheet(self.get_group_style())
        form_layout = QFormLayout(group)
        form_layout.setSpacing(10)
        
        # 界面主题
        self.theme_combo = QComboBox()
        self.theme_combo.setStyleSheet(self.get_combo_style())
        self.theme_combo.addItems(["light", "dark"])
        form_layout.addRow("界面主题:", self.theme_combo)
        
        # 自动保存
        self.auto_save_cb = QCheckBox("自动保存结果")
        self.auto_save_cb.setStyleSheet(self.get_checkbox_style())
        form_layout.addRow(self.auto_save_cb)
        
        # 显示进度
        self.show_progress_cb = QCheckBox("显示进度条")
        self.show_progress_cb.setStyleSheet(self.get_checkbox_style())
        form_layout.addRow(self.show_progress_cb)
        
        layout.addWidget(group)
        
    def create_ocr_settings_group(self, layout):
        """创建OCR设置组"""
        group = QGroupBox("OCR设置")
        group.setStyleSheet(self.get_group_style())
        form_layout = QFormLayout(group)
        form_layout.setSpacing(10)
        

        
        # OCR版本
        self.ocr_version_combo = QComboBox()
        self.ocr_version_combo.setStyleSheet(self.get_combo_style())
        self.ocr_version_combo.addItems(["PP-OCRv4", "PP-OCRv5"])
        form_layout.addRow("OCR版本:", self.ocr_version_combo)
        
        layout.addWidget(group)
        
    def create_translation_settings_group(self, layout):
        """创建翻译设置组"""
        group = QGroupBox("翻译设置")
        group.setStyleSheet(self.get_group_style())
        form_layout = QFormLayout(group)
        form_layout.setSpacing(10)

        # 启用本地预翻译
        self.enable_local_pretranslation_cb = QCheckBox("启用本地预翻译")
        self.enable_local_pretranslation_cb.setStyleSheet(self.get_checkbox_style())
        self.enable_local_pretranslation_cb.setToolTip("启用后使用本地NLLB模型预翻译，然后用LLM优化；禁用后直接使用LLM进行翻译")
        form_layout.addRow(self.enable_local_pretranslation_cb)

        # 压缩阈值
        self.compression_threshold_spin = QDoubleSpinBox()
        self.compression_threshold_spin.setStyleSheet(self.get_spinbox_style())
        self.compression_threshold_spin.setRange(0.1, 10.0)
        self.compression_threshold_spin.setSingleStep(0.1)
        self.compression_threshold_spin.setDecimals(1)
        form_layout.addRow("压缩阈值:", self.compression_threshold_spin)
        
        # 批处理大小
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setStyleSheet(self.get_spinbox_style())
        self.batch_size_spin.setRange(1, 100)
        form_layout.addRow("批处理大小:", self.batch_size_spin)
        
        # 超时时间
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setStyleSheet(self.get_spinbox_style())
        self.timeout_spin.setRange(10, 300)
        self.timeout_spin.setSuffix(" 秒")
        form_layout.addRow("超时时间:", self.timeout_spin)
        
        layout.addWidget(group)
        
    def create_device_settings_group(self, layout):
        """创建设备设置组"""
        group = QGroupBox("设备设置")
        group.setStyleSheet(self.get_group_style())
        form_layout = QFormLayout(group)
        form_layout.setSpacing(10)
        
        # 设备模式
        self.device_mode_combo = QComboBox()
        self.device_mode_combo.setStyleSheet(self.get_combo_style())
        self.device_mode_combo.addItems(["auto", "cuda", "mps", "cpu"])
        form_layout.addRow("设备模式:", self.device_mode_combo)
        
        # 混合精度
        self.mixed_precision_cb = QCheckBox("启用混合精度")
        self.mixed_precision_cb.setStyleSheet(self.get_checkbox_style())
        form_layout.addRow(self.mixed_precision_cb)
        
        layout.addWidget(group)

    def create_llm_prompt_settings_group(self, layout):
        """创建LLM提示词配置组"""
        group = QGroupBox("LLM提示词配置")
        group.setStyleSheet(self.get_group_style())
        form_layout = QFormLayout(group)
        form_layout.setSpacing(10)

        # 系统提示词编辑按钮
        self.system_prompt_btn = QPushButton("📝 编辑系统提示词")
        self.system_prompt_btn.setStyleSheet(self.get_button_style())
        self.system_prompt_btn.setToolTip("编辑LLM的系统提示词，定义AI助手的角色和行为")
        self.system_prompt_btn.clicked.connect(self.edit_system_prompt)
        form_layout.addRow("系统提示词:", self.system_prompt_btn)

        # 用户提示词模板编辑按钮
        self.user_template_btn = QPushButton("📝 编辑用户提示词模板")
        self.user_template_btn.setStyleSheet(self.get_button_style())
        self.user_template_btn.setToolTip("编辑用户提示词模板，定义发送给LLM的消息格式")
        self.user_template_btn.clicked.connect(self.edit_user_template)
        form_layout.addRow("用户提示词模板:", self.user_template_btn)

        layout.addWidget(group)

    def create_google_fonts_group(self, layout):
        """创建Google Fonts API设置组"""
        group = QGroupBox("Google Fonts API设置")
        group.setStyleSheet(self.get_group_style())
        form_layout = QFormLayout(group)
        form_layout.setSpacing(10)

        # API密钥状态显示
        self.api_status_label = QLabel("未配置")
        self.api_status_label.setStyleSheet("color: #ff6b6b; font-weight: bold;")
        form_layout.addRow("API状态:", self.api_status_label)

        # 配置按钮
        self.config_api_btn = QPushButton("配置API密钥")
        self.config_api_btn.setStyleSheet(self.get_button_style())
        self.config_api_btn.clicked.connect(self.show_google_fonts_config_dialog)
        form_layout.addRow(self.config_api_btn)

        layout.addWidget(group)

    def create_button_area(self, layout):
        """创建按钮区域"""
        self.button_frame = QFrame()
        # 样式将在apply_theme中设置

        button_layout = QHBoxLayout(self.button_frame)
        button_layout.setContentsMargins(20, 12, 20, 12)

        # 重置按钮
        self.reset_btn = QPushButton("重置默认")
        # 样式将在apply_theme中设置
        self.reset_btn.clicked.connect(self.reset_to_defaults)

        # 弹性空间
        button_layout.addWidget(self.reset_btn)
        button_layout.addStretch()

        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        # 样式将在apply_theme中设置
        self.cancel_btn.clicked.connect(self.reject)

        # 确定按钮
        self.ok_btn = QPushButton("确定")
        # 样式将在apply_theme中设置
        self.ok_btn.clicked.connect(self.accept_settings)
        self.ok_btn.setDefault(True)

        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.ok_btn)

        layout.addWidget(self.button_frame)

    def on_llm_type_changed(self):
        """处理LLM类型改变"""
        llm_type = self.llm_type_combo.currentData()

        # 根据LLM类型显示/隐藏相应的配置组
        if llm_type == 'api':
            self.compression_model_combo.setVisible(False)
            self.api_config_group.setVisible(True)
        else:
            self.compression_model_combo.setVisible(True)
            self.api_config_group.setVisible(False)

    def on_api_provider_changed(self):
        """处理API提供商改变"""
        provider = self.api_provider_combo.currentData()

        # 清空当前模型选项
        self.api_model_combo.clear()

        # 根据提供商添加对应的模型选项和配置
        if provider == 'openai':
            # 只包含稳定的聊天模型（都使用 chat/completions 端点）
            models = [
                'gpt-4o',           # 最新的GPT-4o模型
                'gpt-4',            # 标准GPT-4
                'gpt-4-turbo',      # GPT-4 Turbo
                'gpt-3.5-turbo',    # GPT-3.5 Turbo
                'gpt-4o-mini',      # GPT-4o Mini
                'chatgpt-4o-latest', # ChatGPT-4o最新版
                'gpt-4.1',          # 新的GPT-4.1模型
                'gpt-4.1-mini'      # GPT-4.1 Mini模型
            ]
            default_url = 'https://api.openai.com/v1'
            placeholder = '请输入OpenAI API密钥 (sk-...)'
            default_model = 'gpt-4o'
        else:
            models = []
            default_url = ''
            placeholder = '请输入API密钥'
            default_model = ''

        # 添加模型选项和自定义选项
        self.api_model_combo.addItems(models)
        if models:  # 如果有预设模型，添加自定义选项
            self.api_model_combo.addItem("自定义模型...")

        # 重置模型选择为默认模型（不保留之前的自定义模型）
        if models:
            # 设置默认模型
            default_index = self.api_model_combo.findText(default_model)
            if default_index >= 0:
                self.api_model_combo.setCurrentIndex(default_index)
            else:
                # 如果找不到默认模型，选择第一个
                self.api_model_combo.setCurrentIndex(0)

        # 同步更新API端点URL
        if hasattr(self, 'api_base_url_edit'):
            self.api_base_url_edit.setText(default_url)  # 直接设置而不是占位符
            self.api_base_url_edit.setPlaceholderText(f"默认: {default_url}")

        # 同步更新API密钥占位符
        self.api_key_edit.setPlaceholderText(placeholder)

        # 提供商变更时清空API密钥（让用户重新输入）
        self.api_key_edit.clear()

    def toggle_api_key_visibility(self):
        """切换API密钥显示/隐藏"""
        if self.api_key_edit.echoMode() == QLineEdit.EchoMode.Password:
            self.api_key_edit.setEchoMode(QLineEdit.EchoMode.Normal)
            self.toggle_key_visibility_btn.setText("🙈")
            self.toggle_key_visibility_btn.setToolTip("隐藏API密钥")
        else:
            self.api_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
            self.toggle_key_visibility_btn.setText("👁")
            self.toggle_key_visibility_btn.setToolTip("显示API密钥")

    def test_api_connection(self):
        """测试API连通性"""
        from ..utils.logger import get_logger
        logger = get_logger()

        provider = self.api_provider_combo.currentData()
        model = self.api_model_combo.currentText()
        api_key = self.api_key_edit.text().strip()
        base_url = self.api_base_url_edit.text().strip()

        logger.info(f"🔍 开始API连通性检测...")
        logger.info(f"   提供商: {provider}")
        logger.info(f"   模型: {model}")
        logger.info(f"   端点: {base_url}")
        logger.info(f"   密钥: {'已设置' if api_key else '未设置'}")

        if not provider:
            logger.error("❌ API检测失败: 未选择API提供商")
            self.show_message("错误", "请选择API提供商", QMessageBox.Icon.Warning)
            return

        if not model or model == "自定义模型...":
            logger.error("❌ API检测失败: 未选择或输入模型名称")
            self.show_message("错误", "请选择或输入模型名称", QMessageBox.Icon.Warning)
            return

        if not api_key:
            logger.error("❌ API检测失败: 未输入API密钥")
            self.show_message("错误", "请输入API密钥", QMessageBox.Icon.Warning)
            return

        # 显示检测进度
        logger.info("🔄 正在检测API连通性...")
        self.test_api_btn.setText("检测中...")
        self.test_api_btn.setEnabled(False)

        # 在后台线程中进行API测试
        from PyQt6.QtCore import QThread, pyqtSignal

        class APITestThread(QThread):
            finished = pyqtSignal(bool, str)

            def __init__(self, provider, model, api_key, base_url, logger):
                super().__init__()
                self.provider = provider
                self.model = model
                self.api_key = api_key
                self.base_url = base_url
                self.logger = logger

            def run(self):
                try:
                    # 直接使用输入的API密钥
                    actual_key = self.api_key
                    self.logger.info("🔑 使用配置的API密钥")

                    # 设置默认URL（修正端点地址）
                    if not self.base_url:
                        if self.provider == 'openai':
                            self.base_url = 'https://api.openai.com/v1'

                    self.logger.info(f"🌐 使用API端点: {self.base_url}")

                    # 测试API连接
                    from openai import OpenAI
                    self.logger.info("📡 创建API客户端...")
                    client = OpenAI(api_key=actual_key, base_url=self.base_url)

                    # 发送测试请求（所有支持的模型都使用chat/completions端点）
                    self.logger.info(f"📤 发送测试请求到模型: {self.model}")
                    self.logger.info("💬 使用 chat/completions 端点")

                    # 所有支持的OpenAI模型都使用 chat/completions 端点
                    response = client.chat.completions.create(
                        model=self.model,
                        messages=[{"role": "user", "content": "Hello"}],
                        max_tokens=5
                    )

                    if response.choices:
                        success_msg = "API连接成功！"
                        self.logger.info(f"✅ {success_msg}")
                        self.logger.info(f"📥 收到响应: {response.choices[0].message.content}")
                        self.finished.emit(True, success_msg)
                    else:
                        error_msg = "API响应异常"
                        self.logger.error(f"❌ {error_msg}")
                        self.finished.emit(False, error_msg)

                except Exception as e:
                    error_msg = str(e)
                    self.logger.error(f"❌ API连接失败: {error_msg}")

                    if "401" in error_msg or "Unauthorized" in error_msg:
                        final_msg = "API密钥无效"
                    elif "404" in error_msg or "Not Found" in error_msg:
                        final_msg = "模型不存在或无权限访问"
                    elif "timeout" in error_msg.lower():
                        final_msg = "连接超时，请检查网络"
                    else:
                        final_msg = f"连接失败: {error_msg}"

                    self.logger.error(f"❌ 最终错误: {final_msg}")
                    self.finished.emit(False, final_msg)

        def on_test_finished(success, message):
            self.test_api_btn.setText("检测")
            self.test_api_btn.setEnabled(True)

            if success:
                logger.info(f"🎉 API检测完成: {message}")
                self.show_message("成功", message, QMessageBox.Icon.Information)
            else:
                logger.error(f"💥 API检测失败: {message}")
                self.show_message("失败", message, QMessageBox.Icon.Critical)

        # 启动测试线程
        self.api_test_thread = APITestThread(provider, model, api_key, base_url, logger)
        self.api_test_thread.finished.connect(on_test_finished)
        self.api_test_thread.start()

    def on_model_selection_changed(self, text):
        """处理模型选择改变"""
        if text == "自定义模型...":
            self.show_custom_model_dialog()

    def show_message(self, title, message, icon=QMessageBox.Icon.Information):
        """显示消息对话框"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)

        # 应用主题样式
        styles = self.theme_manager.get_theme_styles(self.config.get('ui.theme', 'light'))
        msg_box.setStyleSheet(styles.get('message_box', ''))

        msg_box.exec()

    def show_custom_model_dialog(self):
        """显示自定义模型输入对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("自定义模型")
        dialog.setModal(True)
        dialog.resize(400, 150)

        # 应用当前主题
        styles = self.theme_manager.get_theme_styles(self.config.get('ui.theme', 'light'))
        dialog.setStyleSheet(styles['main_window'])

        layout = QVBoxLayout(dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 说明标签
        info_label = QLabel("请输入Hugging Face模型名称（格式：用户名/模型名）：")
        info_label.setStyleSheet(styles.get('label_secondary', ''))
        layout.addWidget(info_label)

        # 模型名称输入框
        model_input = QLineEdit()
        model_input.setPlaceholderText("例如：Qwen/Qwen2.5-7B-Instruct")
        model_input.setStyleSheet(styles.get('line_edit', styles.get('text_edit', '')))
        layout.addWidget(model_input)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet(styles['button_secondary'])
        cancel_btn.clicked.connect(dialog.reject)

        save_btn = QPushButton("保存")
        save_btn.setStyleSheet(styles['button_primary'])
        save_btn.clicked.connect(lambda: self.validate_and_save_model(dialog, model_input.text().strip()))
        save_btn.setDefault(True)

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(save_btn)
        layout.addLayout(button_layout)

        # 如果用户取消，恢复之前的选择
        if dialog.exec() == QDialog.DialogCode.Rejected:
            # 恢复到之前的选择
            current_model = self.config.get('models.compression.default_model', 'Qwen/Qwen2.5-1.5B-Instruct')
            index = self.compression_model_combo.findText(current_model)
            if index >= 0:
                self.compression_model_combo.setCurrentIndex(index)
            else:
                self.compression_model_combo.setCurrentIndex(0)  # 默认选择第一个

    def validate_and_save_model(self, dialog, model_name):
        """验证并保存自定义模型"""
        if not model_name:
            self.show_error_message("请输入模型名称")
            return

        if '/' not in model_name:
            self.show_error_message("模型名称格式不正确，应为：用户名/模型名")
            return

        # 创建简单的验证对话框
        progress_dialog = QDialog(self)
        progress_dialog.setWindowTitle("验证模型")
        progress_dialog.setModal(True)
        progress_dialog.resize(300, 100)

        # 应用主题样式
        styles = self.theme_manager.get_theme_styles(self.config.get('ui.theme', 'light'))
        progress_dialog.setStyleSheet(styles['main_window'])

        layout = QVBoxLayout(progress_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 状态标签
        status_label = QLabel("正在验证模型，请稍候...")
        status_label.setStyleSheet(styles.get('label_secondary', ''))
        layout.addWidget(status_label)

        # 进度条
        from PyQt6.QtWidgets import QProgressBar
        progress_bar = QProgressBar()
        progress_bar.setRange(0, 0)  # 不确定进度
        progress_bar.setStyleSheet(styles['progress_bar'])
        layout.addWidget(progress_bar)

        progress_dialog.show()

        # 处理事件以确保对话框显示
        from PyQt6.QtWidgets import QApplication
        QApplication.processEvents()

        try:
            # 验证模型是否存在
            from huggingface_hub import repo_exists
            exists = repo_exists(model_name)

            progress_dialog.close()

            if exists:
                # 模型存在，添加到下拉框并选择
                self.add_custom_model(model_name)
                dialog.accept()
            else:
                self.show_error_message(f"模型 '{model_name}' 在Hugging Face上不存在，请检查名称是否正确")

        except Exception as e:
            progress_dialog.close()
            self.show_error_message(f"验证模型时出错: {str(e)}")

    def add_custom_model(self, model_name):
        """添加自定义模型到下拉框"""
        # 检查是否已存在
        for i in range(self.compression_model_combo.count() - 1):  # 排除"自定义模型..."选项
            if self.compression_model_combo.itemText(i) == model_name:
                self.compression_model_combo.setCurrentIndex(i)
                return

        # 在"自定义模型..."之前插入新模型
        insert_index = self.compression_model_combo.count() - 1
        self.compression_model_combo.insertItem(insert_index, model_name)
        self.compression_model_combo.setCurrentIndex(insert_index)

    def show_error_message(self, message):
        """显示错误消息"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("错误")
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Icon.Warning)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)

        # 应用当前主题的弹窗样式
        styles = self.theme_manager.get_theme_styles(self.config.get('ui.theme', 'light'))
        msg_box.setStyleSheet(styles['message_box'])

        msg_box.exec()

    def show_google_fonts_config_dialog(self):
        """显示Google Fonts API配置对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Google Fonts API配置")
        dialog.setModal(True)
        dialog.resize(500, 200)

        # 应用当前主题
        styles = self.theme_manager.get_theme_styles(self.config.get('ui.theme', 'light'))
        dialog.setStyleSheet(styles['main_window'])

        layout = QVBoxLayout(dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 说明标签
        info_label = QLabel("请输入Google Fonts API密钥：")
        info_label.setStyleSheet(styles.get('label_secondary', ''))
        layout.addWidget(info_label)

        # API密钥输入框
        api_key_input = QLineEdit()
        api_key_input.setPlaceholderText("输入您的Google Fonts API密钥")
        api_key_input.setStyleSheet(styles.get('line_edit', ''))

        # 加载当前配置的API密钥
        current_api_key = self.config.get('google_fonts.api_key', '')
        if current_api_key:
            api_key_input.setText(current_api_key)

        layout.addWidget(api_key_input)

        # 帮助链接
        help_label = QLabel('<a href="https://developers.google.com/fonts/docs/developer_api?hl=zh-cn#APIKey">如何获取Google Fonts API密钥？</a>')
        help_label.setOpenExternalLinks(True)
        help_label.setStyleSheet(styles.get('label_secondary', ''))
        layout.addWidget(help_label)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 测试按钮
        test_btn = QPushButton("测试API")
        test_btn.setStyleSheet(styles.get('button_secondary', ''))
        test_btn.clicked.connect(lambda: self.test_google_fonts_api(api_key_input.text().strip(), dialog))

        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet(styles.get('button_secondary', ''))
        cancel_btn.clicked.connect(dialog.reject)

        # 保存按钮
        save_btn = QPushButton("保存")
        save_btn.setStyleSheet(styles.get('button_primary', ''))
        save_btn.clicked.connect(lambda: self.save_google_fonts_api(api_key_input.text().strip(), dialog))
        save_btn.setDefault(True)

        button_layout.addWidget(test_btn)
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(save_btn)

        layout.addLayout(button_layout)

        dialog.exec()

    def test_google_fonts_api(self, api_key: str, parent_dialog):
        """测试Google Fonts API密钥"""
        if not api_key:
            self.show_error_message("请输入API密钥")
            return

        # 创建进度对话框
        progress_dialog = QDialog(parent_dialog)
        progress_dialog.setWindowTitle("测试API")
        progress_dialog.setModal(True)
        progress_dialog.resize(300, 100)

        # 应用主题样式
        styles = self.theme_manager.get_theme_styles(self.config.get('ui.theme', 'light'))
        progress_dialog.setStyleSheet(styles['main_window'])

        layout = QVBoxLayout(progress_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 状态标签
        status_label = QLabel("正在测试API密钥，请稍候...")
        status_label.setStyleSheet(styles.get('label_secondary', ''))
        layout.addWidget(status_label)

        # 进度条
        progress_bar = QProgressBar()
        progress_bar.setRange(0, 0)  # 不确定进度
        progress_bar.setStyleSheet(styles.get('progress_bar', ''))
        layout.addWidget(progress_bar)

        progress_dialog.show()
        QApplication.processEvents()

        try:
            # 使用Google Fonts下载器的静态方法测试API
            from src.core.font_management.google_fonts_downloader import GoogleFontsDownloader

            is_valid, message = GoogleFontsDownloader.test_api_key(api_key)

            progress_dialog.close()

            # 显示结果
            msg_box = QMessageBox(parent_dialog)
            if is_valid:
                msg_box.setWindowTitle("测试成功")
                msg_box.setText(f"API密钥验证成功！\n{message}")
                msg_box.setIcon(QMessageBox.Icon.Information)
            else:
                msg_box.setWindowTitle("测试失败")
                msg_box.setText(f"API密钥验证失败：\n{message}")
                msg_box.setIcon(QMessageBox.Icon.Warning)

            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.setStyleSheet(styles['message_box'])
            msg_box.exec()

        except Exception as e:
            progress_dialog.close()
            self.show_error_message(f"测试过程中发生错误：{str(e)}")

    def save_google_fonts_api(self, api_key: str, parent_dialog):
        """保存Google Fonts API密钥"""
        if not api_key:
            self.show_error_message("请输入API密钥")
            return

        # 创建进度对话框
        progress_dialog = QDialog(parent_dialog)
        progress_dialog.setWindowTitle("保存配置")
        progress_dialog.setModal(True)
        progress_dialog.resize(300, 100)

        # 应用主题样式
        styles = self.theme_manager.get_theme_styles(self.config.get('ui.theme', 'light'))
        progress_dialog.setStyleSheet(styles['main_window'])

        layout = QVBoxLayout(progress_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 状态标签
        status_label = QLabel("正在验证并保存API密钥，请稍候...")
        status_label.setStyleSheet(styles.get('label_secondary', ''))
        layout.addWidget(status_label)

        # 进度条
        progress_bar = QProgressBar()
        progress_bar.setRange(0, 0)  # 不确定进度
        progress_bar.setStyleSheet(styles.get('progress_bar', ''))
        layout.addWidget(progress_bar)

        progress_dialog.show()
        QApplication.processEvents()

        try:
            # 先测试API密钥
            from src.core.font_management.google_fonts_downloader import GoogleFontsDownloader

            is_valid, message = GoogleFontsDownloader.test_api_key(api_key)

            progress_dialog.close()

            if not is_valid:
                self.show_error_message(f"API密钥无效：{message}")
                return

            # 保存到配置
            update_config('google_fonts.api_key', api_key)

            # 更新状态显示
            self.update_api_status()

            # 显示成功消息
            msg_box = QMessageBox(parent_dialog)
            msg_box.setWindowTitle("保存成功")
            msg_box.setText("Google Fonts API密钥已保存并验证成功！")
            msg_box.setIcon(QMessageBox.Icon.Information)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)

            msg_box.setStyleSheet(styles['message_box'])
            msg_box.exec()

            # 关闭配置对话框
            parent_dialog.accept()

        except Exception as e:
            progress_dialog.close()
            self.show_error_message(f"保存失败：{str(e)}")

    def update_api_status(self):
        """更新API状态显示"""
        api_key = self.config.get('google_fonts.api_key', '')
        if api_key:
            self.api_status_label.setText("已配置")
            self.api_status_label.setStyleSheet("color: #51cf66; font-weight: bold;")
        else:
            self.api_status_label.setText("未配置")
            self.api_status_label.setStyleSheet("color: #ff6b6b; font-weight: bold;")

    def get_group_style(self):
        """获取组框样式"""
        styles = self.theme_manager.get_theme_styles()
        return styles['group_box']

    def get_combo_style(self):
        """获取下拉框样式"""
        styles = self.theme_manager.get_theme_styles()
        return styles['combo_box']

    def get_checkbox_style(self):
        """获取复选框样式"""
        styles = self.theme_manager.get_theme_styles()
        return styles['checkbox']

    def get_spinbox_style(self):
        """获取数字输入框样式"""
        styles = self.theme_manager.get_theme_styles()
        return styles['spinbox']

    def get_button_style(self):
        """获取按钮样式"""
        styles = self.theme_manager.get_theme_styles()
        return styles.get('button_primary', '')

    def edit_system_prompt(self):
        """编辑系统提示词"""
        from .text_edit_dialog import TextEditDialog

        # 获取当前系统提示词
        current_prompt = self.config.get('llm_prompt.system_prompt', '')

        # 创建编辑对话框
        dialog = TextEditDialog(
            parent=self,
            title="编辑系统提示词",
            initial_text=current_prompt,
            config_key="llm_prompt.system_prompt",
            save_callback=lambda text: update_config('llm_prompt.system_prompt', text)
        )

        # 显示对话框
        dialog.exec()

    def edit_user_template(self):
        """编辑用户提示词模板"""
        from .text_edit_dialog import TextEditDialog

        # 获取当前用户提示词模板
        current_template = self.config.get('llm_prompt.user_template', '')

        # 创建编辑对话框
        dialog = TextEditDialog(
            parent=self,
            title="编辑用户模板提示词",
            initial_text=current_template,
            config_key="llm_prompt.user_template",
            save_callback=lambda text: update_config('llm_prompt.user_template', text)
        )

        # 显示对话框
        dialog.exec()

    def load_settings(self):
        """加载当前设置"""
        # 常规设置
        self.enable_compression_cb.setChecked(
            self.config.get('translation.enable_compression', True)
        )

        # 设置LLM类型
        llm_type = self.config.get('models.compression.type', 'local')
        for i in range(self.llm_type_combo.count()):
            if self.llm_type_combo.itemData(i) == llm_type:
                self.llm_type_combo.setCurrentIndex(i)
                break

        # 触发LLM类型改变事件以显示/隐藏相应组件
        self.on_llm_type_changed()

        # 设置压缩模型
        current_model = self.config.get('models.compression.default_model', 'Qwen/Qwen2.5-1.5B-Instruct')
        index = self.compression_model_combo.findText(current_model)
        if index >= 0:
            self.compression_model_combo.setCurrentIndex(index)
        else:
            # 如果是自定义模型，添加到列表中
            if current_model not in ["Qwen/Qwen2.5-1.5B-Instruct", "Qwen/Qwen2.5-3B-Instruct", "Qwen/Qwen2.5-7B-Instruct"]:
                self.add_custom_model(current_model)
            else:
                self.compression_model_combo.setCurrentIndex(0)  # 默认选择第一个



        # 加载API配置（确保组件存在）
        if hasattr(self, 'api_provider_combo') and hasattr(self, 'api_model_combo'):
            api_config = self.config.get('models.compression.api', {})

            # 设置API提供商
            provider = api_config.get('provider', 'openai')
            for i in range(self.api_provider_combo.count()):
                if self.api_provider_combo.itemData(i) == provider:
                    self.api_provider_combo.setCurrentIndex(i)
                    break

            # 触发提供商改变事件以更新模型列表
            self.on_api_provider_changed()

            # 设置API模型
            api_model = api_config.get('model', 'gpt-4o')
            index = self.api_model_combo.findText(api_model)
            if index >= 0:
                self.api_model_combo.setCurrentIndex(index)
            else:
                # 如果是自定义模型，设置为可编辑文本
                if api_model and api_model != "自定义模型...":
                    self.api_model_combo.setCurrentText(api_model)

            # 设置API密钥
            if hasattr(self, 'api_key_edit'):
                api_key = api_config.get('api_key', '')
                self.api_key_edit.setText(api_key)

            # 设置API端点
            if hasattr(self, 'api_base_url_edit'):
                base_url = api_config.get('base_url', '')
                self.api_base_url_edit.setText(base_url)

        # 高级设置
        self.theme_combo.setCurrentText(self.config.get('ui.theme', 'light'))
        self.auto_save_cb.setChecked(self.config.get('ui.auto_save', True))
        self.show_progress_cb.setChecked(self.config.get('ui.show_progress', True))

        self.ocr_version_combo.setCurrentText(self.config.get('models.ocr.ocr_version', 'PP-OCRv4'))

        self.enable_local_pretranslation_cb.setChecked(self.config.get('translation.enable_local_pretranslation', False))
        self.compression_threshold_spin.setValue(self.config.get('translation.compression_threshold', 1.5))
        self.batch_size_spin.setValue(self.config.get('translation.batch_size', 10))
        self.timeout_spin.setValue(self.config.get('translation.timeout', 30))

        self.device_mode_combo.setCurrentText(self.config.get('device.mode', 'auto'))
        self.mixed_precision_cb.setChecked(self.config.get('device.enable_mixed_precision', True))

        # 更新Google Fonts API状态
        self.update_api_status()

    def accept_settings(self):
        """接受设置并保存"""
        try:
            # 保存设置到配置文件
            self.save_settings()

            # 发送设置改变信号
            self.settingsChanged.emit()

            # 显示成功消息
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("设置")
            msg_box.setText("设置已保存成功！")
            msg_box.setIcon(QMessageBox.Icon.Information)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)

            # 应用当前主题的弹窗样式
            styles = self.theme_manager.get_theme_styles(self.config.get('ui.theme', 'light'))
            msg_box.setStyleSheet(styles['message_box'])

            msg_box.exec()

            # 关闭对话框
            self.accept()

        except Exception as e:
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("错误")
            msg_box.setText(f"保存设置失败: {str(e)}")
            msg_box.setIcon(QMessageBox.Icon.Critical)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)

            # 应用当前主题的弹窗样式
            styles = self.theme_manager.get_theme_styles(self.config.get('ui.theme', 'light'))
            msg_box.setStyleSheet(styles['message_box'])

            msg_box.exec()

    def save_settings(self):
        """保存设置到配置文件"""

        # 常规设置
        update_config('translation.enable_compression', self.enable_compression_cb.isChecked())

        # 保存LLM类型
        llm_type = self.llm_type_combo.currentData()
        update_config('models.compression.type', llm_type)

        # 保存压缩模型（确保不保存"自定义模型..."选项）
        current_model = self.compression_model_combo.currentText()
        if current_model != "自定义模型...":
            update_config('models.compression.default_model', current_model)



        # 保存API配置
        if llm_type == 'api':
            api_provider = self.api_provider_combo.currentData()
            api_model = self.api_model_combo.currentText()
            api_key = self.api_key_edit.text().strip()
            base_url = self.api_base_url_edit.text().strip()

            update_config('models.compression.api.provider', api_provider)
            update_config('models.compression.api.model', api_model)
            update_config('models.compression.api.api_key', api_key)
            update_config('models.compression.api.base_url', base_url)

        # 高级设置
        update_config('ui.theme', self.theme_combo.currentText())
        update_config('ui.auto_save', self.auto_save_cb.isChecked())
        update_config('ui.show_progress', self.show_progress_cb.isChecked())

        update_config('models.ocr.ocr_version', self.ocr_version_combo.currentText())

        update_config('translation.enable_local_pretranslation', self.enable_local_pretranslation_cb.isChecked())
        update_config('translation.compression_threshold', self.compression_threshold_spin.value())
        update_config('translation.batch_size', self.batch_size_spin.value())
        update_config('translation.timeout', self.timeout_spin.value())

        update_config('device.mode', self.device_mode_combo.currentText())
        update_config('device.enable_mixed_precision', self.mixed_precision_cb.isChecked())

    def reset_to_defaults(self):
        """重置为默认设置"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("重置设置")
        msg_box.setText("确定要重置所有设置为默认值吗？")
        msg_box.setIcon(QMessageBox.Icon.Question)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        msg_box.setDefaultButton(QMessageBox.StandardButton.Yes)  # Yes按钮为默认（蓝色）

        # 应用当前主题的弹窗样式
        styles = self.theme_manager.get_theme_styles(self.config.get('ui.theme', 'light'))
        msg_box.setStyleSheet(styles['message_box'])

        reply = msg_box.exec()

        if reply == QMessageBox.StandardButton.Yes:
            # 重置常规设置
            self.enable_compression_cb.setChecked(True)
            # 重置到第一个默认模型
            self.compression_model_combo.setCurrentIndex(0)  # Qwen/Qwen2.5-1.5B-Instruct

            # 重置高级设置
            self.theme_combo.setCurrentText('light')
            self.auto_save_cb.setChecked(True)
            self.show_progress_cb.setChecked(True)

            self.ocr_version_combo.setCurrentText('PP-OCRv4')

            self.enable_local_pretranslation_cb.setChecked(False)
            self.compression_threshold_spin.setValue(1.5)
            self.batch_size_spin.setValue(10)
            self.timeout_spin.setValue(30)

            self.device_mode_combo.setCurrentText('auto')
            self.mixed_precision_cb.setChecked(True)

    def apply_theme(self, theme_name: str = None):
        """应用主题"""
        if theme_name is None:
            theme_name = self.config.get('ui.theme', 'light')

        # 更新主题管理器的当前主题
        self.theme_manager.set_current_theme(theme_name)

        # 获取主题样式
        styles = self.theme_manager.get_theme_styles(theme_name)

        # 应用对话框和标签页样式
        if theme_name == 'dark':
            self.setStyleSheet("""
                QDialog {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QTabWidget::pane {
                    border: 1px solid #555555;
                    background-color: #404040;
                }
                QTabBar::tab {
                    background-color: #3c3c3c;
                    color: #ffffff;
                    padding: 8px 16px;
                    border: 1px solid #555555;
                    border-bottom: none;
                    margin-right: 2px;
                }
                QTabBar::tab:selected {
                    background-color: #4285f4;
                    border-bottom: 1px solid #4285f4;
                }
                QTabBar::tab:hover {
                    background-color: #404040;
                }
                QScrollArea {
                    border: none;
                    background-color: #404040;
                }
                QWidget {
                    background-color: #404040;
                    color: #ffffff;
                }
            """)
        else:
            self.setStyleSheet("""
                QDialog {
                    background-color: #ffffff;
                    color: #202124;
                }
                QTabWidget::pane {
                    border: 1px solid #dadce0;
                    background-color: white;
                }
                QTabBar::tab {
                    background-color: #f8f9fa;
                    color: #202124;
                    padding: 8px 16px;
                    border: 1px solid #dadce0;
                    border-bottom: none;
                    margin-right: 2px;
                }
                QTabBar::tab:selected {
                    background-color: white;
                    border-bottom: 1px solid white;
                }
                QTabBar::tab:hover {
                    background-color: #e8f0fe;
                }
                QScrollArea {
                    border: none;
                    background-color: white;
                }
                QWidget {
                    background-color: white;
                    color: #202124;
                }
            """)

        # 应用按钮样式
        if hasattr(self, 'reset_btn'):
            self.reset_btn.setStyleSheet(styles['button_secondary'])
        if hasattr(self, 'cancel_btn'):
            self.cancel_btn.setStyleSheet(styles['button_secondary'])
        if hasattr(self, 'ok_btn'):
            self.ok_btn.setStyleSheet(styles['button_primary'])

        # 应用按钮框架样式
        if hasattr(self, 'button_frame'):
            self.button_frame.setStyleSheet(styles['frame_button'])

        # 应用常规设置页面的组件样式
        if hasattr(self, 'compression_group'):
            self.compression_group.setStyleSheet(styles.get('group_box', ''))
        if hasattr(self, 'enable_compression_cb'):
            self.enable_compression_cb.setStyleSheet(styles.get('checkbox', ''))
        if hasattr(self, 'llm_type_combo'):
            self.llm_type_combo.setStyleSheet(styles.get('combo_box', ''))
        if hasattr(self, 'compression_model_combo'):
            self.compression_model_combo.setStyleSheet(styles.get('combo_box', ''))


        # 应用API配置组件样式
        if hasattr(self, 'api_config_group'):
            self.api_config_group.setStyleSheet(styles.get('group_box', ''))
        if hasattr(self, 'api_provider_combo'):
            self.api_provider_combo.setStyleSheet(styles.get('combo_box', ''))
        if hasattr(self, 'api_model_combo'):
            self.api_model_combo.setStyleSheet(styles.get('combo_box', ''))
        if hasattr(self, 'api_key_edit'):
            self.api_key_edit.setStyleSheet(styles.get('line_edit', ''))
        if hasattr(self, 'api_base_url_edit'):
            self.api_base_url_edit.setStyleSheet(styles.get('line_edit', ''))
        if hasattr(self, 'toggle_key_visibility_btn'):
            self.toggle_key_visibility_btn.setStyleSheet(styles.get('button_secondary', ''))
        if hasattr(self, 'test_api_btn'):
            self.test_api_btn.setStyleSheet(styles.get('button_primary', ''))
